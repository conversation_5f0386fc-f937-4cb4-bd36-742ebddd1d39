import React, { useState, useEffect } from "react";
import { X, Loader2, Plus, Trash2, Check } from "lucide-react";
import confetti from "canvas-confetti";
import { motion } from "framer-motion";
import { getWeekInfo, canAddDataForWeek, getWeekDataRestrictionMessage, getLastCompletedWeekInfo, type WeekInfo } from "@/lib/utils/weekUtils";
import { type KpiComprasData } from "@/app/actions/kpis-compras";
import { createProveedor } from "@/app/actions/proveedores";
import { useProveedores } from "@/hooks/useProveedores";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import AlertDialog from "@/components/ui/AlertDialog";
import { DeleteProvidersDialog } from "@/components/DeleteProvidersDialog";
// Componente Tooltip personalizado estilo Chart.js
const Tooltip: React.FC<{ children: React.ReactNode; content: string; className?: string }> = ({
  children,
  content,
  className = ""
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: true, left: false, right: false, center: true });
  const tooltipRef = React.useRef<HTMLDivElement>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);

  const updatePosition = React.useCallback(() => {
    if (!containerRef.current || !tooltipRef.current) return;

    const container = containerRef.current.getBoundingClientRect();
    const tooltip = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    // Determinar posición vertical (arriba o abajo)
    const spaceBelow = viewport.height - container.bottom;
    const spaceAbove = container.top;
    const tooltipHeight = tooltip.height || 80; // altura estimada más realista

    const showAbove = spaceBelow < tooltipHeight + 20 && spaceAbove > tooltipHeight + 20;

    // Determinar posición horizontal (centrado, izquierda o derecha)
    const containerCenter = container.left + container.width / 2;
    const tooltipWidth = tooltip.width || 280; // ancho estimado más realista
    const halfTooltipWidth = tooltipWidth / 2;
    const margin = 20; // margen de seguridad

    let showLeft = false;
    let showRight = false;
    let showCenter = true;

    // Verificar si el tooltip centrado se sale del viewport
    if (containerCenter - halfTooltipWidth < margin) {
      showLeft = true;
      showCenter = false;
    } else if (containerCenter + halfTooltipWidth > viewport.width - margin) {
      showRight = true;
      showCenter = false;
    }

    setPosition({
      top: !showAbove,
      left: showLeft,
      right: showRight,
      center: showCenter
    });
  }, []);

  React.useEffect(() => {
    if (isVisible) {
      updatePosition();
      window.addEventListener('resize', updatePosition);
      window.addEventListener('scroll', updatePosition);
      return () => {
        window.removeEventListener('resize', updatePosition);
        window.removeEventListener('scroll', updatePosition);
      };
    }
  }, [isVisible, updatePosition]);

  const getTooltipClasses = () => {
    let classes = "absolute px-3 py-2 text-xs font-medium text-white rounded-md shadow-lg pointer-events-none transition-opacity duration-200";

    if (position.top) {
      classes += " top-full mt-2";
    } else {
      classes += " bottom-full mb-2";
    }

    if (position.center) {
      classes += " left-1/2 transform -translate-x-1/2";
    } else if (position.left) {
      classes += " left-0";
    } else if (position.right) {
      classes += " right-0";
    }

    return classes;
  };

  const getArrowClasses = () => {
    let classes = "absolute w-2 h-2 transform rotate-45";

    if (position.top) {
      classes += " -top-1";
    } else {
      classes += " -bottom-1";
    }

    if (position.center) {
      classes += " left-1/2 -translate-x-1/2";
    } else if (position.left) {
      classes += " left-3";
    } else if (position.right) {
      classes += " right-3";
    }

    return classes;
  };

  const getArrowStyle = () => {
    return {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      borderRight: 'none',
      borderBottom: 'none'
    };
  };

  return (
    <div
      ref={containerRef}
      className={`relative inline-block ${className}`}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div
          ref={tooltipRef}
          className={getTooltipClasses()}
          style={{
            zIndex: 9999,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            backdropFilter: 'blur(4px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            maxWidth: '280px',
            width: 'max-content',
            whiteSpace: 'normal',
            wordWrap: 'break-word',
            lineHeight: '1.4'
          }}
        >
          {content}
          <div
            className={getArrowClasses()}
            style={getArrowStyle()}
          ></div>
        </div>
      )}
    </div>
  );
};

// Interfaz para proveedores en el formulario que permite null en porcentaje
interface FormProveedorData {
  nombre: string;
  porcentaje: number | null;
}

// Interfaz para el formulario que permite valores string o number
interface FormKpiComprasData extends Omit<KpiComprasData, 'numeroProveedoresActivos' | 'porcentajeReporteGanancia' | 'preciosPromedioCompra' | 'diferencialPrecioPemex' | 'distribucionProveedores'> {
  numeroProveedoresActivos: number | string;
  porcentajeReporteGanancia: number | string;
  preciosPromedioCompra: number | string;
  diferencialPrecioPemex: number | string;
  distribucionProveedores: FormProveedorData[];
}

interface KpiComprasInlineFormProps {
  onClose: () => void;
  onSave: (data: KpiComprasData) => Promise<void>;
  editingKpi?: KpiComprasData | null;
  isAddingOldWeek?: boolean;
  existingKpis?: KpiComprasData[];
  onEdit?: (kpi: KpiComprasData) => void;
}

const KpiComprasInlineForm: React.FC<KpiComprasInlineFormProps> = ({
  onClose,
  onSave,
  editingKpi,
  isAddingOldWeek = false,
  existingKpis = [],
  onEdit
}) => {
  const [loading, setLoading] = useState(false);
  const [currentWeek, setCurrentWeek] = useState<WeekInfo | null>(null);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedWeek, setSelectedWeek] = useState<number>(1);
  const [showInvalidDataAlert, setShowInvalidDataAlert] = useState(false);
  const [invalidDataMessage, setInvalidDataMessage] = useState("");
  const [showWeekRestrictionAlert, setShowWeekRestrictionAlert] = useState(false);
  const [weekRestrictionMessage, setWeekRestrictionMessage] = useState("");
  const [showValidationErrorAlert, setShowValidationErrorAlert] = useState(false);
  const [validationErrorMessage, setValidationErrorMessage] = useState("");

  // Estados para manejo de duplicados y modo read-only
  const [isReadOnlyMode, setIsReadOnlyMode] = useState(false);
  const [existingData, setExistingData] = useState<KpiComprasData | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [showSuccessBanner, setShowSuccessBanner] = useState(false);

  // Hook para proveedores optimizado
  const {
    proveedores: proveedoresDB,
    loading: loadingProveedores,
    getAvailableProveedores,
    addProveedorToCache,
    invalidateCache,
    loadProveedores
  } = useProveedores();
  
  // Estados para crear nuevo proveedor
  const [showNewProveedorInput, setShowNewProveedorInput] = useState<number | null>(null);
  const [newProveedorName, setNewProveedorName] = useState("");
  const [creatingProveedor, setCreatingProveedor] = useState(false);

  // Estados para eliminar proveedores
  const [showDeleteProveedorDialog, setShowDeleteProveedorDialog] = useState(false);

  const [formData, setFormData] = useState<FormKpiComprasData>({
    year: new Date().getFullYear(),
    weekNumber: 1,
    weekStartDate: "",
    weekEndDate: "",
    numeroProveedoresActivos: "" as any,
    porcentajeReporteGanancia: "" as any,
    preciosPromedioCompra: "" as any,
    diferencialPrecioPemex: "" as any,
    distribucionProveedores: [
      { nombre: "", porcentaje: null },
      { nombre: "", porcentaje: null },
      { nombre: "", porcentaje: null }
    ]
  });

  // Función para verificar si ya existen datos para la semana
  const checkExistingData = (year: number, weekNumber: number) => {
    const existing = existingKpis.find(kpi =>
      kpi.year === year && kpi.weekNumber === weekNumber
    );

    if (existing) {
      setExistingData(existing);
      setIsReadOnlyMode(true);
      setShowSuccessBanner(true);

      // Cargar los datos existentes en el formulario
      setFormData({
        year: existing.year,
        weekNumber: existing.weekNumber,
        weekStartDate: typeof existing.weekStartDate === 'string' ? existing.weekStartDate : new Date(existing.weekStartDate).toISOString(),
        weekEndDate: typeof existing.weekEndDate === 'string' ? existing.weekEndDate : new Date(existing.weekEndDate).toISOString(),
        numeroProveedoresActivos: existing.numeroProveedoresActivos,
        porcentajeReporteGanancia: existing.porcentajeReporteGanancia,
        preciosPromedioCompra: existing.preciosPromedioCompra,
        diferencialPrecioPemex: existing.diferencialPrecioPemex,
        distribucionProveedores: existing.distribucionProveedores
      });
    } else {
      setExistingData(null);
      setIsReadOnlyMode(false);
      setShowSuccessBanner(false);
    }
  };

  // Inicializar formulario después de cargar proveedores
  useEffect(() => {
    if (proveedoresDB.length > 0) {
      if (editingKpi) {
        // Modo edición
        setFormData({
          year: editingKpi.year,
          weekNumber: editingKpi.weekNumber,
          weekStartDate: typeof editingKpi.weekStartDate === 'string' ? editingKpi.weekStartDate : new Date(editingKpi.weekStartDate).toISOString(),
          weekEndDate: typeof editingKpi.weekEndDate === 'string' ? editingKpi.weekEndDate : new Date(editingKpi.weekEndDate).toISOString(),
          numeroProveedoresActivos: editingKpi.numeroProveedoresActivos,
          porcentajeReporteGanancia: editingKpi.porcentajeReporteGanancia,
          preciosPromedioCompra: editingKpi.preciosPromedioCompra,
          diferencialPrecioPemex: editingKpi.diferencialPrecioPemex,
          distribucionProveedores: editingKpi.distribucionProveedores
        });
        
        const weekInfo = getWeekInfo(editingKpi.year, editingKpi.weekNumber);
        setCurrentWeek(weekInfo);
        setSelectedYear(editingKpi.year);
        setSelectedWeek(editingKpi.weekNumber);

        // En modo edición, no verificar duplicados
        setIsReadOnlyMode(false);
        setShowSuccessBanner(false);
      } else if (isAddingOldWeek) {
        // Para agregar semana antigua, inicializar con valores vacíos
        // La lógica específica se maneja en un useEffect separado
        const defaultProveedores = proveedoresDB.length >= 3 ? [
          { nombre: proveedoresDB[0].nombre, porcentaje: 0 },
          { nombre: proveedoresDB[1].nombre, porcentaje: 0 },
          { nombre: proveedoresDB[2].nombre, porcentaje: 0 }
        ] : [
          { nombre: "", porcentaje: 0 },
          { nombre: "", porcentaje: 0 },
          { nombre: "", porcentaje: 0 }
        ];

        try {
          const weekInfo = getWeekInfo(selectedYear, selectedWeek);
          setCurrentWeek(weekInfo);

          // Asegurar que las fechas estén en formato ISO correcto
          const startDateISO = new Date(weekInfo.startDate).toISOString();
          const endDateISO = new Date(weekInfo.endDate).toISOString();

          setFormData({
            year: selectedYear,
            weekNumber: selectedWeek,
            weekStartDate: startDateISO,
            weekEndDate: endDateISO,
            numeroProveedoresActivos: "" as any,
            porcentajeReporteGanancia: "" as any,
            preciosPromedioCompra: "" as any,
            diferencialPrecioPemex: "" as any,
            distribucionProveedores: defaultProveedores
          });
        } catch (error) {
          console.error("Error al inicializar semana antigua:", error);
        }
      } else {
        // Para nueva entrada de datos, usar la última semana completada
        const defaultProveedores = proveedoresDB.length >= 3 ? [
          { nombre: proveedoresDB[0].nombre, porcentaje: 0 },
          { nombre: proveedoresDB[1].nombre, porcentaje: 0 },
          { nombre: proveedoresDB[2].nombre, porcentaje: 0 }
        ] : [
          { nombre: "", porcentaje: 0 },
          { nombre: "", porcentaje: 0 },
          { nombre: "", porcentaje: 0 }
        ];

        try {
          const weekInfo = getLastCompletedWeekInfo();
          setCurrentWeek(weekInfo);
          setSelectedYear(weekInfo.year);
          setSelectedWeek(weekInfo.weekNumber);

          // Verificar si ya existen datos para esta semana
          checkExistingData(weekInfo.year, weekInfo.weekNumber);

          // Asegurar que las fechas estén en formato ISO correcto
          const startDateISO = new Date(weekInfo.startDate).toISOString();
          const endDateISO = new Date(weekInfo.endDate).toISOString();

          setFormData({
            year: weekInfo.year,
            weekNumber: weekInfo.weekNumber,
            weekStartDate: startDateISO,
            weekEndDate: endDateISO,
            numeroProveedoresActivos: "" as any,
            porcentajeReporteGanancia: "" as any,
            preciosPromedioCompra: "" as any,
            diferencialPrecioPemex: "" as any,
            distribucionProveedores: defaultProveedores
          });
        } catch (error) {
          console.error("Error al inicializar última semana completada:", error);
        }
      }
    }
  }, [editingKpi, isAddingOldWeek, proveedoresDB.length]);

  // useEffect separado para manejar cambios de año/semana en modo agregar semana antigua
  useEffect(() => {
    if (isAddingOldWeek && !editingKpi && proveedoresDB.length > 0) {
      try {
        const weekInfo = getWeekInfo(selectedYear, selectedWeek);
        setCurrentWeek(weekInfo);

        // Asegurar que las fechas estén en formato ISO correcto
        const startDateISO = new Date(weekInfo.startDate).toISOString();
        const endDateISO = new Date(weekInfo.endDate).toISOString();

        setFormData(prev => ({
          ...prev,
          year: selectedYear,
          weekNumber: selectedWeek,
          weekStartDate: startDateISO,
          weekEndDate: endDateISO
          // Mantener distribucionProveedores existentes
        }));
      } catch (error) {
        console.error("Error al actualizar semana:", error);
      }
    }
  }, [selectedYear, selectedWeek, isAddingOldWeek, editingKpi, proveedoresDB.length]);

  const handleInputChange = (field: keyof FormKpiComprasData, value: string) => {
    const newFormData = {
      ...formData,
      [field]: value
    };
    setFormData(newFormData);

    // Detectar cambios comparando todos los campos con los datos existentes
    if (existingData) {
      const hasAnyChange = Object.keys(newFormData).some(key => {
        if (key === 'year' || key === 'weekNumber' || key === 'weekStartDate' || key === 'weekEndDate') {
          return false; // Ignorar campos de metadatos
        }
        if (key === 'distribucionProveedores') {
          // Comparar arrays de proveedores
          const newProveedores = newFormData[key as keyof FormKpiComprasData] as any[];
          const existingProveedores = existingData[key as keyof KpiComprasData] as any[];
          return JSON.stringify(newProveedores) !== JSON.stringify(existingProveedores);
        }
        const formValue = String(newFormData[key as keyof FormKpiComprasData]);
        const existingValue = String(existingData[key as keyof KpiComprasData]);
        return formValue !== existingValue;
      });
      setHasChanges(hasAnyChange);
    } else {
      setHasChanges(true); // Si no hay datos existentes, siempre hay cambios
    }
  };

  // Funciones para manejar proveedores dinámicos
  const handleProveedorChange = (index: number, field: 'nombre' | 'porcentaje', value: string | number | null) => {
    const newFormData = {
      ...formData,
      distribucionProveedores: formData.distribucionProveedores.map((proveedor, i) =>
        i === index
          ? {
              ...proveedor,
              [field]: field === 'porcentaje'
                ? (value === "" || value === null || value === undefined ? null : Number(value))
                : value
            }
          : proveedor
      )
    };
    setFormData(newFormData);

    // Detectar cambios para proveedores
    if (existingData) {
      const hasAnyChange = JSON.stringify(newFormData.distribucionProveedores) !== JSON.stringify(existingData.distribucionProveedores) ||
        Object.keys(newFormData).some(key => {
          if (key === 'year' || key === 'weekNumber' || key === 'weekStartDate' || key === 'weekEndDate' || key === 'distribucionProveedores') {
            return false;
          }
          const formValue = String(newFormData[key as keyof FormKpiComprasData]);
          const existingValue = String(existingData[key as keyof KpiComprasData]);
          return formValue !== existingValue;
        });
      setHasChanges(hasAnyChange);
    } else {
      setHasChanges(true);
    }
  };

  const addProveedor = () => {
    // Siempre agregar un proveedor vacío para que el usuario pueda seleccionar
    setFormData(prev => ({
      ...prev,
      distribucionProveedores: [
        ...prev.distribucionProveedores,
        { nombre: "", porcentaje: 0 }
      ]
    }));
  };

  const clearAllProveedores = () => {
    // Limpiar todos los proveedores y dejar solo uno vacío
    setFormData(prev => ({
      ...prev,
      distribucionProveedores: [{ nombre: "", porcentaje: 0 }]
    }));
  };

  // Función para crear un nuevo proveedor
  const handleCreateNewProveedor = async (index: number, nombre: string) => {
    if (!nombre.trim()) return;

    try {
      setCreatingProveedor(true);
      const result = await createProveedor({
        nombre: nombre.trim(),
        activo: true
      });

      if (result.success && result.data) {
        // Convertir el proveedor al formato esperado
        const proveedorData = {
          ...result.data,
          descripcion: result.data.descripcion || undefined
        };

        // Agregar el nuevo proveedor al cache
        addProveedorToCache(proveedorData);

        // Actualizar el formulario con el nuevo proveedor
        handleProveedorChange(index, 'nombre', result.data.nombre);
        
        // Limpiar estados
        setShowNewProveedorInput(null);
        setNewProveedorName("");
      } else {
        alert(result.error || "Error al crear el proveedor");
      }
    } catch (error) {
      console.error("Error al crear proveedor:", error);
      alert("Error al crear el proveedor");
    } finally {
      setCreatingProveedor(false);
    }
  };

  // Función para cancelar la creación de nuevo proveedor
  const handleCancelNewProveedor = (index: number) => {
    setShowNewProveedorInput(null);
    setNewProveedorName("");
    // Si el proveedor estaba vacío, eliminarlo
    if (!formData.distribucionProveedores[index].nombre) {
      removeProveedor(index);
    }
  };

  const removeProveedor = (index: number) => {
    // Permitir eliminar si hay más de un proveedor
    if (formData.distribucionProveedores.length > 1) {
      setFormData(prev => ({
        ...prev,
        distribucionProveedores: prev.distribucionProveedores.filter((_, i) => i !== index)
      }));
    }
  };

  // Calcular el total de porcentajes (excluyendo opciones especiales)
  const totalPorcentaje = formData.distribucionProveedores.reduce((sum, proveedor) => {
    // Excluir proveedores vacíos y "none"
    if (!proveedor.nombre ||
        proveedor.nombre === "none" ||
        proveedor.nombre === "") {
      return sum;
    }
    return sum + (proveedor.porcentaje || 0);
  }, 0);

  // Función para manejar cambio de año
  const handleYearChange = (year: number) => {
    if (isNaN(year) || year < 1900 || year > 2100) {
      console.error("Año inválido:", year);
      return;
    }

    setSelectedYear(year);
    try {
      const weekInfo = getWeekInfo(year, selectedWeek);
      setCurrentWeek(weekInfo);

      // Asegurar que las fechas estén en formato ISO correcto
      const startDateISO = new Date(weekInfo.startDate).toISOString();
      const endDateISO = new Date(weekInfo.endDate).toISOString();

      // Mantener los proveedores seleccionados al cambiar año
      setFormData(prev => ({
        ...prev,
        year: year,
        weekStartDate: startDateISO,
        weekEndDate: endDateISO
        // NO resetear distribucionProveedores
      }));
    } catch (error) {
      console.error("Error al cambiar año:", error);
    }
  };

  // Función para manejar cambio de semana
  const handleWeekChange = (week: number) => {
    if (isNaN(week) || week < 1 || week > 53) {
      console.error("Semana inválida:", week);
      return;
    }

    setSelectedWeek(week);
    try {
      const weekInfo = getWeekInfo(selectedYear, week);
      setCurrentWeek(weekInfo);

      // Asegurar que las fechas estén en formato ISO correcto
      const startDateISO = new Date(weekInfo.startDate).toISOString();
      const endDateISO = new Date(weekInfo.endDate).toISOString();

      // Mantener los proveedores seleccionados al cambiar semana
      setFormData(prev => ({
        ...prev,
        weekNumber: week,
        weekStartDate: startDateISO,
        weekEndDate: endDateISO
        // NO resetear distribucionProveedores
      }));
    } catch (error) {
      console.error("Error al cambiar semana:", error);
    }
  };

  // Función para verificar si ya existe un KPI para la semana seleccionada
  const checkDuplicateKpi = (year: number, weekNumber: number): boolean => {
    if (editingKpi || isReadOnlyMode) return false; // Si estamos editando o en modo read-only, no verificar duplicados

    return existingKpis.some(kpi =>
      kpi.year === year && kpi.weekNumber === weekNumber
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Verificar si ya existe un KPI para esta semana (solo para nuevos KPIs)
    if (checkDuplicateKpi(formData.year, formData.weekNumber)) {
      setInvalidDataMessage(`Los KPIs de la semana ${formData.weekNumber}/${formData.year} ya están registrados. Puedes editarlos usando el botón "Editar" o seleccionar una semana diferente.`);
      setShowInvalidDataAlert(true);
      return;
    }

    // Validar restricciones de semana (solo para nuevos KPIs, no para edición)
    if (!editingKpi && !isReadOnlyMode && !canAddDataForWeek(formData.year, formData.weekNumber)) {
      setWeekRestrictionMessage(getWeekDataRestrictionMessage(formData.year, formData.weekNumber));
      setShowWeekRestrictionAlert(true);
      return;
    }

    // Validar porcentajes de proveedores
    const proveedoresReales = formData.distribucionProveedores.filter(p =>
      p.nombre &&
      p.nombre !== "none" &&
      p.nombre !== "0% - Ningún proveedor" &&
      p.nombre !== ""
    );

    // Validar que si hay proveedores reales, la suma debe ser 100%
    // Permitir el caso donde no hay proveedores seleccionados (todos vacíos o "0% - Ningún proveedor")
    if (proveedoresReales.length > 0) {
      if (Math.abs(totalPorcentaje - 100) > 0.1) {
        setInvalidDataMessage("La suma de porcentajes de proveedores debe ser exactamente 100%");
        setShowInvalidDataAlert(true);
        return;
      }
    }
    // Si no hay proveedores reales, está bien que la suma sea 0%
    // (caso donde todos son "0% - Ningún proveedor" o están vacíos)

    setLoading(true);

    try {
      // Convertir strings a números
      const dataToSave: KpiComprasData = {
        ...formData,
        numeroProveedoresActivos: Number(formData.numeroProveedoresActivos),
        porcentajeReporteGanancia: Number(formData.porcentajeReporteGanancia),
        preciosPromedioCompra: Number(formData.preciosPromedioCompra),
        diferencialPrecioPemex: Number(formData.diferencialPrecioPemex),
        distribucionProveedores: formData.distribucionProveedores.map(p => ({
          nombre: p.nombre,
          porcentaje: p.porcentaje || 0
        }))
      };

      // Si estamos en modo read-only (actualizando datos existentes), incluir el ID
      if (isReadOnlyMode && existingData?.id) {
        dataToSave.id = existingData.id;
      }
      // Si estamos editando un KPI específico, incluir su ID
      else if (editingKpi?.id) {
        dataToSave.id = editingKpi.id;
      }



      await onSave(dataToSave);

      // Mostrar confetti
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });

      // Limpiar estados de nuevo proveedor
      setShowNewProveedorInput(null);
      setNewProveedorName("");
      setCreatingProveedor(false);
      onClose();
    } catch (error) {
      console.error("Error al guardar KPI de compras:", error);
      if (error instanceof Error && (error.message.includes("ZodError") || error.message.includes("must be less than or equal to"))) {
        setValidationErrorMessage("Los datos ingresados no son válidos. Por favor verifica que todos los valores estén dentro de los rangos permitidos.");
        setShowValidationErrorAlert(true);
      } else {
        setInvalidDataMessage("Error al guardar los datos. Por favor, verifica la información e intenta nuevamente.");
        setShowInvalidDataAlert(true);
      }
    } finally {
      setLoading(false);
    }
  };

  const kpiFields = [
    {
      key: "numeroProveedoresActivos" as keyof FormKpiComprasData,
      title: "NÚMERO DE PROVEEDORES ACTIVOS",
      label: "Número de Proveedores Activos",
      description: "Proveedores con al menos una transacción en el mes",
      type: "number",
      min: 0,
      max: 1000,
      step: 1
    },
    {
      key: "porcentajeReporteGanancia" as keyof FormKpiComprasData,
      title: "PORCENTAJE DE REPORTE DE GANANCIA OPERATIVA (%)",
      label: "Porcentaje de Reporte de Ganancia Operativa (%)",
      description: "Porcentaje de transacciones que reportaron ganancia operativa",
      type: "number",
      min: 0,
      max: 500,
      step: 0.1
    },
    {
      key: "preciosPromedioCompra" as keyof FormKpiComprasData,
      title: "PRECIOS PROMEDIO DE COMPRA ($/L)",
      label: "Precios Promedio de Compra ($/L)",
      description: "Precio promedio por litro de combustible comprado",
      type: "number",
      min: 0,
      max: 1000,
      step: 0.01
    },
    {
      key: "diferencialPrecioPemex" as keyof FormKpiComprasData,
      title: "DIFERENCIAL PRECIO PEMEX (%)",
      label: "Diferencial entre Precio de Compra Real y Precio de Terminal PEMEX (%)",
      description: "Porcentaje de diferencial entre precio de compra real y precio de terminal PEMEX",
      type: "number",
      min: -100,
      max: 100,
      step: 0.01
    }
  ];

  return (
    <>
    <motion.div
      className="space-y-8 p-6"
      initial={{ opacity: 0, y: 8 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -8 }}
      transition={{ duration: 0.2, ease: "easeOut" }}
    >
      {/* Banner de éxito para datos existentes */}
      {showSuccessBanner && existingData && (
        <motion.div
          className="bg-green-500/10 border border-green-500/30 rounded-lg p-4 mb-2"
          initial={{ opacity: 0, scale: 0.98 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.15, delay: 0.05 }}
        >
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22 4 12 14.01 9 11.01"></polyline>
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-green-600">
                Semana {existingData.weekNumber} registrada ({currentWeek?.startDate.toLocaleDateString('es-ES', { day: '2-digit', month: 'short' })} – {currentWeek?.endDate.toLocaleDateString('es-ES', { day: '2-digit', month: 'short' })})
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Alertas */}
      {showInvalidDataAlert && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <X className="h-5 w-5 text-red-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error en los datos</h3>
              <div className="mt-2 text-sm text-red-700">
                <p>{invalidDataMessage}</p>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => setShowInvalidDataAlert(false)}
                  className="bg-red-100 px-2 py-1.5 rounded-md text-sm font-medium text-red-800 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                >
                  Cerrar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showWeekRestrictionAlert && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <X className="h-5 w-5 text-yellow-400" />
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Restricción de semana</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>{weekRestrictionMessage}</p>
              </div>
              <div className="mt-4">
                <button
                  type="button"
                  onClick={() => setShowWeekRestrictionAlert(false)}
                  className="bg-yellow-100 px-2 py-1.5 rounded-md text-sm font-medium text-yellow-800 hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                >
                  Cerrar
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Header */}
      <motion.div
        className="mb-6"
        initial={{ opacity: 0, y: 5 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2, delay: 0.05 }}
      >
        <div className="flex items-center gap-3 mb-2">
          <h2 className="text-xl md:text-xl lg:text-2xl text-black font-medium">
            {editingKpi ? "Editar" : isAddingOldWeek ? "Agregar Semana Antigua" : isReadOnlyMode ? "Datos registrados" : "Agregar nueva semana"} - Compras
          </h2>
          {isReadOnlyMode && existingData && onEdit && (
            <button
              onClick={() => onEdit(existingData)}
              className="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-primary/10 transition-colors"
              title="Modificar los datos registrados"
            >
              Editar
            </button>
          )}
        </div>

        {/* Badge de semana - mostrar en todos los modos excepto Agregar Semana Antigua */}
        {!isAddingOldWeek && currentWeek && (
          <div className="mb-4">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-xs lg:text-sm font-medium bg-primary/10 text-primary">
              Semana {currentWeek.weekNumber}/{currentWeek.year} ({currentWeek.startDate.toLocaleDateString()} - {currentWeek.endDate.toLocaleDateString()})
            </span>
          </div>
        )}
      </motion.div>

      {/* Form */}
      <motion.form
        onSubmit={handleSubmit}
        className="space-y-6"
        initial={{ opacity: 0, y: 6 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.25, delay: 0.1 }}
      >
        {/* Selectores de Año y Semana para semanas antiguas */}
        {isAddingOldWeek && !editingKpi && (
          <div className="bg-primary/10 rounded-lg p-3">
            <h3 className="text-sm font-medium text-blue-800 mb-2">Seleccionar Semana</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="flex flex-col h-full">
                <label className="block text-sm font-medium text-blue-800 mb-1">
                  Año
                </label>
                <Select
                  value={selectedYear.toString()}
                  onValueChange={(value) => {
                    const year = parseInt(value);
                    if (!isNaN(year)) {
                      handleYearChange(year);
                    }
                  }}
                >
                  <SelectTrigger className="w-full bg-white border-blue-300 focus:border-blue-500 focus:ring-blue-500">
                    <SelectValue placeholder="Seleccionar año" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: new Date().getFullYear() - 2021 + 1 }, (_, i) => new Date().getFullYear() - i).map((year) => (
                      <SelectItem key={year} value={year.toString()}>
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex flex-col h-full">
                <label className="block text-sm font-medium text-blue-800 mb-1">
                  Semana
                </label>
                <Select
                  value={selectedWeek.toString()}
                  onValueChange={(value) => {
                    const week = parseInt(value);
                    if (!isNaN(week)) {
                      handleWeekChange(week);
                    }
                  }}
                >
                  <SelectTrigger className="w-full bg-white border-blue-300 focus:border-blue-500 focus:ring-blue-500">
                    <SelectValue placeholder="Seleccionar semana" />
                  </SelectTrigger>
                  <SelectContent>
                    {Array.from({ length: 52 }, (_, i) => i + 1).map((week) => (
                      <SelectItem key={week} value={week.toString()}>
                        Semana {week}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            {/* Badge con fecha de la semana seleccionada */}
            {currentWeek && (
              <div className="mt-3 flex">
                <div className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                  <svg className="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                  </svg>
                  {currentWeek.startDate.toLocaleDateString('es-ES', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric'
                  })} - {currentWeek.endDate.toLocaleDateString('es-ES', {
                    day: '2-digit',
                    month: 'short',
                    year: 'numeric'
                  })}
                </div>
              </div>
            )}
          </div>
        )}

        {/* Sección de Distribución por Proveedores */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Tooltip content="Distribución porcentual de compras por proveedor">
                <h3 className="text-sm font-bold text-gray-600 uppercase tracking-wide cursor-default">
                  % COMPRA POR PROVEEDOR
                </h3>
              </Tooltip>
              <p className="text-sm">
                Total: <span className="text-black font-medium">{totalPorcentaje.toFixed(1)}%</span>
                {(() => {
                  const proveedoresReales = formData.distribucionProveedores.filter(p => 
                    p.nombre && 
                    p.nombre !== "none" && 
                    p.nombre !== "0% - Ningún proveedor" && 
                    p.nombre !== ""
                  );
                  
                  if (proveedoresReales.length > 0 && totalPorcentaje !== 100) {
                    return (
                      <span className="ml-1 text-red-600">
                        La suma de porcentajes debe ser 100 %.
                      </span>
                    );
                  } else if (proveedoresReales.length === 0 && totalPorcentaje === 0) {
                    return (
                      <span className="ml-1 text-green-600">
                        No hay proveedores seleccionados.
                      </span>
                    );
                  }
                  return null;
                })()}
              </p>
            </div>
            {!isReadOnlyMode && (
              <div className="flex gap-2">
                <button
                  type="button"
                  onClick={addProveedor}
                  className="inline-flex gap-2 items-center border border-primary text-primary rounded-md focus:shadow-none focus:outline focus:outline-primary/40 transition-all hover:shadow-lg hover:bg-primary hover:text-white hover:shadow-primary/30 py-2 px-4 text-xs font-semibold"
                >
                  <Plus className="h-4 w-4" />
                  Agregar Proveedor
                </button>
                {formData.distribucionProveedores.length > 1 && (
                  <button
                    type="button"
                    onClick={clearAllProveedores}
                    className="inline-flex gap-2 items-center border border-red-500 text-red-500 rounded-md focus:shadow-none focus:outline focus:outline-red-500/40 transition-all hover:shadow-lg hover:bg-red-500 hover:text-white hover:shadow-red-500/30 py-2 px-4 text-xs font-semibold"
                    title="Limpiar todos los proveedores"
                  >
                    <Trash2 className="h-4 w-4" />
                    Limpiar Todo
                  </button>
                )}
              </div>
            )}
          </div>

          <div className="space-y-2">
            {formData.distribucionProveedores.map((proveedor, index) => {
              // Obtener proveedores disponibles (no usados en otros selects)
              const proveedoresUsados = formData.distribucionProveedores
                .filter((_, i) => i !== index)
                .map(p => p.nombre);
              const proveedoresDisponibles = getAvailableProveedores(proveedoresUsados);

              return (
                <div key={index} className="flex items-center gap-3 pt-3 pb-3 rounded-lg">
                  <div className="flex-1">
                    {showNewProveedorInput === index ? (
                      // Input para crear nuevo proveedor
                      <div className="flex items-center gap-2">
                        <input
                          type="text"
                          placeholder="Nombre del nuevo proveedor"
                          value={newProveedorName}
                          onChange={(e) => setNewProveedorName(e.target.value)}
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 md:text-sm text-md disabled:cursor-not-allowed disabled:opacity-50"
                          maxLength={100}
                          disabled={creatingProveedor}
                        />
                        <button
                          type="button"
                          onClick={() => handleCreateNewProveedor(index, newProveedorName)}
                          disabled={!newProveedorName.trim() || creatingProveedor}
                          className="flex items-center justify-center w-8 h-8 text-green-600 bg-green-500/10 rounded-lg hover:bg-green-500/20 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-green-500/10 transition-colors"
                          title="Confirmar nuevo proveedor"
                        >
                          {creatingProveedor ? (
                            <Loader2 className="h-4 w-4 animate-spin" />
                          ) : (
                            <Check className="h-4 w-4" />
                          )}
                        </button>
                        <button
                          type="button"
                          onClick={() => handleCancelNewProveedor(index)}
                          disabled={creatingProveedor}
                          className="flex items-center justify-center w-8 h-8 text-red-600 bg-red-500/10 rounded-lg hover:bg-red-500/20 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-red-500/10 transition-colors"
                          title="Cancelar"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ) : (
                      // Select normal para proveedores existentes
                      <Select
                        value={proveedor.nombre || "none"}
                        onValueChange={(value) => {
                          if (isReadOnlyMode) return;
                          if (value === "create_new") {
                            setShowNewProveedorInput(index);
                            setNewProveedorName("");
                          } else if (value === "delete_provider") {
                            // Abrir dialog para eliminar proveedores
                            setShowDeleteProveedorDialog(true);
                            // No cambiar el valor del select
                          } else if (value === "none") {
                            handleProveedorChange(index, 'nombre', "");
                            handleProveedorChange(index, 'porcentaje', null);
                          } else {
                            handleProveedorChange(index, 'nombre', value);
                          }
                        }}
                        disabled={isReadOnlyMode}
                      >
                        <SelectTrigger className={`w-full ${isReadOnlyMode ? 'bg-gray-100 cursor-not-allowed' : ''}`}>
                          <SelectValue placeholder="Seleccionar proveedor" />
                        </SelectTrigger>
                        <SelectContent>
                          {loadingProveedores ? (
                            <SelectItem value="loading" disabled>
                              Cargando proveedores...
                            </SelectItem>
                          ) : (
                            <>
                              {/* Opción para crear nuevo proveedor - PRIMERO */}
                              <SelectItem value="create_new" className="text-primary font-medium data-[highlighted]:text-primary data-[highlighted]:bg-blue-50">
                                + Crear nuevo proveedor
                              </SelectItem>

                              {/* Opción para eliminar proveedores - SEGUNDO */}
                              <SelectItem value="delete_provider" className="text-red-600 font-medium data-[highlighted]:text-red-600 data-[highlighted]:bg-red-50">
                                <div className="flex items-center gap-2">
                                  <Trash2 className="h-4 w-4" />
                                  <span>Eliminar proveedores</span>
                                </div>
                              </SelectItem>

                              {/* Separador */}
                              <div className="border-t border-gray-200 my-1"></div>

                              {/* Opción por defecto */}
                              <SelectItem value="none" className="text-gray-500">
                                Seleccionar proveedor...
                              </SelectItem>

                              {/* Mostrar el proveedor actual si está seleccionado y no está en disponibles */}
                              {proveedor.nombre &&
                               proveedor.nombre !== "none" &&
                               proveedor.nombre !== "" &&
                               !proveedoresDisponibles.find(p => p.nombre === proveedor.nombre) && (
                                <SelectItem value={proveedor.nombre}>
                                  {proveedor.nombre}
                                </SelectItem>
                              )}

                              {/* Mostrar proveedores disponibles */}
                              {proveedoresDisponibles.map((p) => (
                                <SelectItem key={p.id} value={p.nombre}>
                                  {p.nombre}
                                </SelectItem>
                              ))}
                            </>
                          )}
                        </SelectContent>
                      </Select>
                    )}
                  </div>
                  <div className="w-24">
                    <input
                      type="text"
                      inputMode="decimal"
                      pattern="[0-9]*\.?[0-9]*"
                      placeholder="0"
                      value={proveedor.porcentaje !== undefined && proveedor.porcentaje !== null ? proveedor.porcentaje.toString() : ''}
                      onChange={(e) => {
                        // Solo permitir números y punto decimal
                        const value = e.target.value.replace(/[^0-9.]/g, '');
                        // Evitar múltiples puntos decimales
                        const parts = value.split('.');
                        const cleanValue = parts.length > 2 ? parts[0] + '.' + parts.slice(1).join('') : value;
                        handleProveedorChange(index, 'porcentaje', cleanValue);
                      }}
                      onKeyDown={(e) => {
                        // Permitir: números, punto decimal, backspace, delete, tab, enter, flechas
                        if (!/[0-9.]/.test(e.key) &&
                            !['Backspace', 'Delete', 'Tab', 'Enter', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.key)) {
                          e.preventDefault();
                        }
                        // Evitar múltiples puntos decimales
                        if (e.key === '.' && e.currentTarget.value.includes('.')) {
                          e.preventDefault();
                        }
                      }}
                      maxLength={5}
                      disabled={proveedor.nombre === "" || isReadOnlyMode}
                      className={`w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent text-center ${
                        proveedor.nombre === "" || isReadOnlyMode
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                          : ''
                      }`}
                      required={!!(proveedor.nombre && proveedor.nombre !== "")}
                    />
                  </div>
                  <span className="text-sm text-gray-500">%</span>
                  {!isReadOnlyMode && (
                    <button
                      type="button"
                      onClick={() => removeProveedor(index)}
                      disabled={formData.distribucionProveedores.length <= 1}
                      className={`p-1.5 rounded transition-colors ${
                        formData.distribucionProveedores.length <= 1
                          ? 'text-gray-300 cursor-not-allowed'
                          : 'text-red-600 bg-red-500/10 hover:bg-red-500/20'
                      } disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-red-500/10`}
                      title={formData.distribucionProveedores.length <= 1 ? "Debe haber al menos un proveedor" : "Eliminar proveedor"}
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Campos Numéricos de KPIs */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
          {kpiFields.map((field, index) => (
            <motion.div
              key={field.key}
              className="space-y-2"
              initial={{ opacity: 0, y: 4 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.2, delay: 0.15 + (index * 0.02) }}
            >
              <div className="space-y-1">
                <label className="font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-600 text-md cursor-default">
                  {field.title}
                </label>
              </div>
              <input
                type="text"
                inputMode="decimal"
                pattern={field.key === 'diferencialPrecioPemex' ? "-?[0-9]*\.?[0-9]*" : "[0-9]*\.?[0-9]*"}
                placeholder={field.key === 'diferencialPrecioPemex' ? "Ej: -1.25 o 2.50" : "Ingrese un valor numérico"}
                value={formData[field.key] as string | number}
                onChange={(e) => handleInputChange(field.key, e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 md:text-sm text-md disabled:cursor-not-allowed disabled:opacity-50"
                required
                readOnly={isReadOnlyMode}
                disabled={isReadOnlyMode}
              />
            </motion.div>
          ))}
        </div>

        {/* Botones */}
        <motion.div
          className="flex items-center justify-start space-x-4 pt-2"
          initial={{ opacity: 0, y: 4 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2, delay: 0.3 }}
        >
          <button
            type="button"
            onClick={() => {
              // Limpiar estados de nuevo proveedor
              setShowNewProveedorInput(null);
              setNewProveedorName("");
              setCreatingProveedor(false);
              onClose();
            }}
            className="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:border-transparent transition-colors"
          >
            {isReadOnlyMode ? "Cerrar" : "Cancelar"}
          </button>
          {(!isReadOnlyMode || hasChanges) && (
            <button
              type="submit"
              disabled={loading}
              className="inline-flex items-center text-md bg-primary text-white font-medium leading-6 text-center align-middle select-none py-2 px-4 rounded-md transition-all hover:shadow-lg hover:shadow-primary/80 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {isReadOnlyMode && hasChanges ? "Actualizando..." : "Guardando..."}
                </>
              ) : (
                <>
                  {isReadOnlyMode && hasChanges ? "Actualizar" : "Guardar"}
                  <span className="w-4 h-4 ms-1">
                    <svg className="w-full h-full text-white" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
                      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                        <rect id="bound" x="0" y="0" width="24" height="24"></rect>
                        <path d="M3,13.5 L19,12 L3,10.5 L3,3.7732928 C3,3.70255344 3.01501031,3.63261921 3.04403925,3.56811047 C3.15735832,3.3162903 3.45336217,3.20401298 3.70518234,3.31733205 L21.9867539,11.5440392 C22.098181,11.5941815 22.1873901,11.6833905 22.2375323,11.7948177 C22.3508514,12.0466378 22.2385741,12.3426417 21.9867539,12.4559608 L3.70518234,20.6826679 C3.64067359,20.7116969 3.57073936,20.7267072 3.5,20.7267072 C3.22385763,20.7267072 3,20.5028496 3,20.2267072 L3,13.5 Z" id="Combined-Shape" fill="currentcolor"></path>
                      </g>
                    </svg>
                  </span>
                </>
              )}
            </button>
          )}
        </motion.div>
      </motion.form>
    </motion.div>

    {/* Alert Dialog para errores de validación */}
    <AlertDialog
      isOpen={showValidationErrorAlert}
      onClose={() => setShowValidationErrorAlert(false)}
      title="Error de validación"
      message={validationErrorMessage}
      type="error"
      buttonText="Corregir"
    />

    {/* Dialog para eliminar proveedores */}
    <DeleteProvidersDialog
      open={showDeleteProveedorDialog}
      onOpenChange={setShowDeleteProveedorDialog}
      providers={proveedoresDB}
      onSuccess={() => {
        // Invalidar cache para recargar proveedores
        invalidateCache();
      }}
      onRefreshProviders={() => {
        // Forzar recarga inmediata de proveedores
        loadProveedores(true);
      }}
    />
  </>
  );
};

export default KpiComprasInlineForm;
